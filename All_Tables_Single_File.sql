-- Complete Database Structure - All Tables
-- Dynamic Form Configuration System

-- =====================================================
-- FORM CONFIGURATION TABLES
-- =====================================================

-- 1. Collections Table
CREATE TABLE collections (
    id SERIAL PRIMARY KEY,
    collection_name VARCHAR(255) NOT NULL,
    collection_desc VARCHAR(500),
    collection_api_id VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. Field Types Table
CREATE TABLE field_types (
    id SERIAL PRIMARY KEY,
    field_type_name VARCHAR(100) NOT NULL,
    field_type_desc VARCHAR(500),
    display_name VARCHAR(100) NOT NULL,
    help_text VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. Components Table
CREATE TABLE components (
    id SERIAL PRIMARY KEY,
    component_name VARCHAR(255) NOT NULL,
    component_display_name VARCHAR(255) NOT NULL,
    component_api_id VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    get_url VARCHAR(500),
    post_url VARCHAR(500),
    update_url VARCHAR(500),
    additional_information TEXT,
    additional_info_image VARCHAR(500),
    parent_component_id INT REFERENCES components(id) ON DELETE SET NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. Collection Components Table
CREATE TABLE collection_components (
    id SERIAL PRIMARY KEY,
    collection_id INT REFERENCES collections(id) ON DELETE CASCADE,
    component_id INT REFERENCES components(id) ON DELETE CASCADE,
    display_preference INT NOT NULL,
    is_repeatable BOOLEAN DEFAULT FALSE,
    min_repeat_occurrences INT,
    max_repeat_occurrences INT,
    is_active BOOLEAN DEFAULT TRUE,
    name VARCHAR(255),
    display_name VARCHAR(255),
    additional_info TEXT,
    additional_info_image VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. Fields Table
CREATE TABLE fields (
    id SERIAL PRIMARY KEY,
    field_type_id INT REFERENCES field_types(id) ON DELETE RESTRICT,
    component_id INT REFERENCES components(id) ON DELETE CASCADE,
    collection_id INT REFERENCES collections(id) ON DELETE CASCADE,
    display_preference INT NOT NULL,
    field_name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 6. Field Properties Table
CREATE TABLE field_properties (
    id SERIAL PRIMARY KEY,
    field_id INT REFERENCES fields(id) ON DELETE CASCADE,
    property_name VARCHAR(100) NOT NULL,
    property_value TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 7. Field Validations Table
CREATE TABLE field_validations (
    id SERIAL PRIMARY KEY,
    field_id INT REFERENCES fields(id) ON DELETE CASCADE,
    validation_type VARCHAR(100) NOT NULL,
    validation_value TEXT,
    error_message VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- MASTER DATA TABLES
-- =====================================================

-- 8. Country Regions Master Table
CREATE TABLE country_regions (
    id SERIAL PRIMARY KEY,
    region_code VARCHAR(100) UNIQUE NOT NULL,
    region_name VARCHAR(255) NOT NULL,
    country_code VARCHAR(10),
    country_name VARCHAR(255),
    region_type VARCHAR(50),
    parent_region_id INT REFERENCES country_regions(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 9. States Master Table
CREATE TABLE states (
    id SERIAL PRIMARY KEY,
    state_name VARCHAR(255) NOT NULL,
    state_code VARCHAR(100) UNIQUE,
    country_region_id INT REFERENCES country_regions(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 10. Cities Master Table
CREATE TABLE cities (
    id SERIAL PRIMARY KEY,
    city_name VARCHAR(255) NOT NULL,
    city_code VARCHAR(100) UNIQUE,
    state_id INT REFERENCES states(id) ON DELETE SET NULL,
    country_region_id INT REFERENCES country_regions(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 11. Airports Master Table
CREATE TABLE airports (
    id SERIAL PRIMARY KEY,
    airport_name VARCHAR(255) NOT NULL,
    airport_code VARCHAR(10) UNIQUE,
    iata_code VARCHAR(3),
    icao_code VARCHAR(4),
    city_id INT REFERENCES cities(id) ON DELETE SET NULL,
    country_region_id INT REFERENCES country_regions(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 12. Terminals Master Table
CREATE TABLE terminals (
    id SERIAL PRIMARY KEY,
    terminal_name VARCHAR(255) NOT NULL,
    terminal_code VARCHAR(50) UNIQUE,
    airport_id INT REFERENCES airports(id) ON DELETE CASCADE,
    terminal_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 13. Service Types Master Table
CREATE TABLE service_types (
    id SERIAL PRIMARY KEY,
    service_type_name VARCHAR(255) NOT NULL,
    service_type_code VARCHAR(100) UNIQUE,
    service_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 14. Customers Master Table
CREATE TABLE customers (
    id SERIAL PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_code VARCHAR(100) UNIQUE,
    customer_type VARCHAR(100),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 15. Vendors Master Table
CREATE TABLE vendors (
    id SERIAL PRIMARY KEY,
    vendor_name VARCHAR(255) NOT NULL,
    vendor_code VARCHAR(100) UNIQUE,
    vendor_type VARCHAR(100),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 16. Designations Master Table
CREATE TABLE designations (
    id SERIAL PRIMARY KEY,
    designation_name VARCHAR(100) NOT NULL,
    designation_code VARCHAR(50) UNIQUE,
    designation_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 17. Entities Master Table
CREATE TABLE entities (
    id SERIAL PRIMARY KEY,
    entity_name VARCHAR(255) NOT NULL,
    entity_code VARCHAR(100) UNIQUE,
    entity_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 18. Roles Master Table
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    role_name VARCHAR(255) NOT NULL,
    role_code VARCHAR(100) UNIQUE,
    role_description TEXT,
    permissions TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 19. User Types Master Table
CREATE TABLE user_types (
    id SERIAL PRIMARY KEY,
    user_type_name VARCHAR(100) NOT NULL,
    user_type_code VARCHAR(50) UNIQUE,
    user_type_description TEXT,
    permissions TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- USER MANAGEMENT TABLES
-- =====================================================

-- 20. User Details Table
CREATE TABLE user_details (
    id SERIAL PRIMARY KEY,
    employee_code VARCHAR(100) UNIQUE,
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    email_id VARCHAR(255) UNIQUE NOT NULL,
    designation_id INT REFERENCES designations(id) ON DELETE SET NULL,
    entity VARCHAR(500),
    role VARCHAR(500),
    is_super_user BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);

-- 21. User Entity Mapping Table
CREATE TABLE user_entity_mapping (
    id SERIAL PRIMARY KEY,
    user_id INT REFERENCES user_details(id) ON DELETE CASCADE,
    entity_id INT REFERENCES entities(id) ON DELETE CASCADE,
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(user_id, entity_id)
);

-- 22. User Role Mapping Table
CREATE TABLE user_role_mapping (
    id SERIAL PRIMARY KEY,
    user_id INT REFERENCES user_details(id) ON DELETE CASCADE,
    role_id INT REFERENCES roles(id) ON DELETE CASCADE,
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(user_id, role_id)
);

-- =====================================================
-- ROLE MANAGEMENT TABLES
-- =====================================================

-- 23. Role Details Table
CREATE TABLE role_details (
    id SERIAL PRIMARY KEY,
    role_id VARCHAR(100) UNIQUE,
    role_name VARCHAR(255) NOT NULL,
    role_status BOOLEAN DEFAULT TRUE,
    select_entity VARCHAR(500),
    role_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);

-- 24. Role Entity Mapping Table
CREATE TABLE role_entity_mapping (
    id SERIAL PRIMARY KEY,
    role_detail_id INT REFERENCES role_details(id) ON DELETE CASCADE,
    entity_id INT REFERENCES entities(id) ON DELETE CASCADE,
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(role_detail_id, entity_id)
);

-- =====================================================
-- MASTER MANAGEMENT TABLES
-- =====================================================

-- 25. Master Management Table
CREATE TABLE master_management (
    id SERIAL PRIMARY KEY,
    master_name VARCHAR(255) NOT NULL,
    master_code VARCHAR(100) UNIQUE,
    master_type VARCHAR(100),
    master_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);

-- =====================================================
-- POINT MANAGEMENT TABLES
-- =====================================================

-- 26. Points Table
CREATE TABLE points (
    id SERIAL PRIMARY KEY,
    point_id VARCHAR(100) UNIQUE,
    country_region_id_1 INT REFERENCES country_regions(id) ON DELETE SET NULL,
    country_region_id_2 INT REFERENCES country_regions(id) ON DELETE SET NULL,
    country_region_id_3 INT REFERENCES country_regions(id) ON DELETE SET NULL,
    country_region_id_4 INT REFERENCES country_regions(id) ON DELETE SET NULL,
    point_name VARCHAR(255),
    point_description TEXT,
    coordinates VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);

-- =====================================================
-- CUSTOMER CONFLICT MANAGEMENT TABLES
-- =====================================================

-- 27. Customer Conflicts Table
CREATE TABLE customer_conflicts (
    id SERIAL PRIMARY KEY,
    address TEXT,
    city_name VARCHAR(255),
    city_id INT REFERENCES cities(id) ON DELETE SET NULL,
    country_region_id INT REFERENCES country_regions(id) ON DELETE SET NULL,
    state VARCHAR(255),
    state_id INT REFERENCES states(id) ON DELETE SET NULL,
    ref_airport_rec_id INT REFERENCES airports(id) ON DELETE SET NULL,
    ref_terminal_rec_id INT REFERENCES terminals(id) ON DELETE SET NULL,
    service_type VARCHAR(255),
    service_type_id INT REFERENCES service_types(id) ON DELETE SET NULL,
    city_rec_id INT REFERENCES cities(id) ON DELETE SET NULL,
    ref_address_region_rec_id INT REFERENCES country_regions(id) ON DELETE SET NULL,
    ref_customer_rec_id INT REFERENCES customers(id) ON DELETE SET NULL,
    ref_vendor_rec_id INT REFERENCES vendors(id) ON DELETE SET NULL,
    conflict_status VARCHAR(100) DEFAULT 'OPEN',
    conflict_description TEXT,
    resolution_notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);

-- =====================================================
-- ROUTE MANAGEMENT TABLES
-- =====================================================

-- 28. Routes Table
CREATE TABLE routes (
    id SERIAL PRIMARY KEY,
    route_name VARCHAR(255) NOT NULL,
    reference_from_airport_record_id INT REFERENCES airports(id) ON DELETE SET NULL,
    reference_to_airport_record_id INT REFERENCES airports(id) ON DELETE SET NULL,
    route_code VARCHAR(100) UNIQUE,
    distance_km DECIMAL(10,2),
    estimated_duration_minutes INT,
    route_status VARCHAR(50) DEFAULT 'ACTIVE',
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);

-- =====================================================
-- OFFER MANAGEMENT TABLES
-- =====================================================

-- 29. Offers Table
CREATE TABLE offers (
    id SERIAL PRIMARY KEY,
    offer_title VARCHAR(255) NOT NULL,
    upload_file VARCHAR(500),
    file_name VARCHAR(255),
    file_size BIGINT,
    file_type VARCHAR(100),
    description TEXT NOT NULL,
    start_date DATE,
    expiry_date DATE,
    users_type_id INT REFERENCES user_types(id) ON DELETE SET NULL,
    offer_status VARCHAR(50) DEFAULT 'ACTIVE',
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);

-- =====================================================
-- FILE MANAGEMENT TABLES
-- =====================================================

-- 30. File Uploads Table
CREATE TABLE file_uploads (
    id SERIAL PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    original_file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    file_type VARCHAR(100),
    mime_type VARCHAR(100),
    table_name VARCHAR(100),
    record_id INT,
    field_name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    uploaded_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    uploaded_by INT REFERENCES user_details(id) ON DELETE SET NULL
);

-- =====================================================
-- ADDITIONAL MASTER DATA TABLES FOR TENDER
-- =====================================================

-- 31. Tender Types Master Table
CREATE TABLE tender_types (
    id SERIAL PRIMARY KEY,
    tender_type_name VARCHAR(255) NOT NULL,
    tender_type_code VARCHAR(100) UNIQUE,
    tender_type_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 32. Companies Master Table
CREATE TABLE companies (
    id SERIAL PRIMARY KEY,
    company_name VARCHAR(255) NOT NULL,
    company_code VARCHAR(100) UNIQUE,
    company_type VARCHAR(100),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    address TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 33. Fuel Types Master Table
CREATE TABLE fuel_types (
    id SERIAL PRIMARY KEY,
    fuel_type_name VARCHAR(255) NOT NULL,
    fuel_type_code VARCHAR(100) UNIQUE,
    fuel_description TEXT,
    fuel_category VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 34. Bid Status Master Table
CREATE TABLE bid_status (
    id SERIAL PRIMARY KEY,
    status_name VARCHAR(100) NOT NULL,
    status_code VARCHAR(50) UNIQUE,
    status_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 35. Incumbent Master Table
CREATE TABLE incumbents (
    id SERIAL PRIMARY KEY,
    incumbent_name VARCHAR(255) NOT NULL,
    incumbent_code VARCHAR(100) UNIQUE,
    incumbent_type VARCHAR(100),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 36. Tender Locations Master Table
CREATE TABLE tender_locations (
    id SERIAL PRIMARY KEY,
    location_name VARCHAR(255) NOT NULL,
    location_code VARCHAR(100) UNIQUE,
    city_id INT REFERENCES cities(id) ON DELETE SET NULL,
    airport_id INT REFERENCES airports(id) ON DELETE SET NULL,
    country_region_id INT REFERENCES country_regions(id) ON DELETE SET NULL,
    location_type VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- TENDER MANAGEMENT TABLES
-- =====================================================

-- 37. Tenders Table
CREATE TABLE tenders (
    id SERIAL PRIMARY KEY,
    tender_doc VARCHAR(500),
    project_tender_id VARCHAR(255),
    tender_type_id INT REFERENCES tender_types(id) ON DELETE SET NULL,
    company_id INT REFERENCES companies(id) ON DELETE SET NULL,
    customer_id VARCHAR(255),
    customer_ref_id INT REFERENCES customers(id) ON DELETE SET NULL,
    fuel_type_id INT REFERENCES fuel_types(id) ON DELETE SET NULL,
    no_of_rounds INT,
    bid_status_id INT REFERENCES bid_status(id) ON DELETE SET NULL,
    incumbent_id INT REFERENCES incumbents(id) ON DELETE SET NULL,
    bid_deadline DATE,
    tender_status VARCHAR(100) DEFAULT 'OPEN',
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);

-- 38. Tender Location Mapping Table
CREATE TABLE tender_location_mapping (
    id SERIAL PRIMARY KEY,
    tender_id INT REFERENCES tenders(id) ON DELETE CASCADE,
    tender_location_id INT REFERENCES tender_locations(id) ON DELETE CASCADE,
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(tender_id, tender_location_id)
);

-- =====================================================
-- TICKET MANAGEMENT MASTER DATA TABLES
-- =====================================================

-- 39. Ticket Types Master Table
CREATE TABLE ticket_types (
    id SERIAL PRIMARY KEY,
    ticket_type_name VARCHAR(255) NOT NULL,
    ticket_type_code VARCHAR(100) UNIQUE,
    ticket_type_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 40. Ticket Priorities Master Table
CREATE TABLE ticket_priorities (
    id SERIAL PRIMARY KEY,
    priority_name VARCHAR(100) NOT NULL,
    priority_code VARCHAR(50) UNIQUE,
    priority_level INT,
    priority_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 41. Item Types Master Table
CREATE TABLE item_types (
    id SERIAL PRIMARY KEY,
    item_type_name VARCHAR(255) NOT NULL,
    item_type_code VARCHAR(100) UNIQUE,
    item_category VARCHAR(100),
    item_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- TICKET MANAGEMENT TABLES
-- =====================================================

-- 42. Tickets Table
CREATE TABLE tickets (
    id SERIAL PRIMARY KEY,
    subject VARCHAR(500) NOT NULL,
    description TEXT,
    ticket_type_id INT REFERENCES ticket_types(id) ON DELETE SET NULL,
    item_type VARCHAR(255),
    item_type_id INT REFERENCES item_types(id) ON DELETE SET NULL,
    upload_file VARCHAR(500),
    file_name VARCHAR(255),
    file_size BIGINT,
    file_type VARCHAR(100),
    ticket_priority_id INT REFERENCES ticket_priorities(id) ON DELETE SET NULL,
    customer_vendor_name VARCHAR(255),
    customer_id INT REFERENCES customers(id) ON DELETE SET NULL,
    vendor_id INT REFERENCES vendors(id) ON DELETE SET NULL,
    ticket_number VARCHAR(100) UNIQUE,
    ticket_status VARCHAR(100) DEFAULT 'OPEN',
    assigned_to INT REFERENCES user_details(id) ON DELETE SET NULL,
    resolution_notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);

-- 43. Ticket Status History Table
CREATE TABLE ticket_status_history (
    id SERIAL PRIMARY KEY,
    ticket_id INT REFERENCES tickets(id) ON DELETE CASCADE,
    old_status VARCHAR(100),
    new_status VARCHAR(100) NOT NULL,
    status_changed_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    status_change_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    change_notes TEXT
);

-- 44. Ticket Comments Table
CREATE TABLE ticket_comments (
    id SERIAL PRIMARY KEY,
    ticket_id INT REFERENCES tickets(id) ON DELETE CASCADE,
    comment_text TEXT NOT NULL,
    is_internal BOOLEAN DEFAULT FALSE,
    commented_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    comment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- =====================================================
-- MASTER FIELDS MANAGEMENT TABLES
-- =====================================================

-- 45. Master Fields Table
CREATE TABLE master_fields (
    id SERIAL PRIMARY KEY,
    master_management_id INT REFERENCES master_management(id) ON DELETE CASCADE,
    field_name VARCHAR(255) NOT NULL,
    field_type VARCHAR(100),
    field_type_id INT REFERENCES field_types(id) ON DELETE SET NULL,
    is_mandatory BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);

-- 46. Master Field Values Table
CREATE TABLE master_field_values (
    id SERIAL PRIMARY KEY,
    master_field_id INT REFERENCES master_fields(id) ON DELETE CASCADE,
    field_value TEXT,
    display_order INT,
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL
);

-- 47. Dynamic Master Data Table
CREATE TABLE dynamic_master_data (
    id SERIAL PRIMARY KEY,
    master_management_id INT REFERENCES master_management(id) ON DELETE CASCADE,
    record_data JSONB,
    record_status VARCHAR(100) DEFAULT 'ACTIVE',
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);

-- =====================================================
-- FORM POPUP MANAGEMENT TABLES
-- =====================================================

-- 48. Form Popups Table
CREATE TABLE form_popups (
    id SERIAL PRIMARY KEY,
    form_id INT UNIQUE,
    form_name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);

-- =====================================================
-- ACTIVITY MANAGEMENT MASTER DATA TABLES
-- =====================================================

-- 49. Activity Types Master Table
CREATE TABLE activity_types (
    id SERIAL PRIMARY KEY,
    activity_type_name VARCHAR(255) NOT NULL,
    activity_type_code VARCHAR(100) UNIQUE,
    activity_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 50. Responsible Persons Master Table
CREATE TABLE responsible_persons (
    id SERIAL PRIMARY KEY,
    person_name VARCHAR(255) NOT NULL,
    person_code VARCHAR(100) UNIQUE,
    email VARCHAR(255),
    phone VARCHAR(20),
    department VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- ACTIVITY MANAGEMENT TABLES
-- =====================================================

-- 51. Activities Table
CREATE TABLE activities (
    id SERIAL PRIMARY KEY,
    opportunity VARCHAR(255),
    contact_number VARCHAR(20),
    email VARCHAR(255),
    responsible VARCHAR(255),
    responsible_id INT REFERENCES responsible_persons(id) ON DELETE SET NULL,
    expected_revenue DECIMAL(15,2),
    due_date DATE,
    activity_type VARCHAR(100),
    activity_type_id INT REFERENCES activity_types(id) ON DELETE SET NULL,
    summary VARCHAR(500),
    activity_status VARCHAR(100) DEFAULT 'PENDING',
    priority VARCHAR(50),
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);

-- =====================================================
-- LEAD MANAGEMENT MASTER DATA TABLES
-- =====================================================

-- 52. Lead Priorities Master Table
CREATE TABLE lead_priorities (
    id SERIAL PRIMARY KEY,
    priority_name VARCHAR(100) NOT NULL,
    priority_code VARCHAR(50) UNIQUE,
    priority_level INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 53. Lead Sources Master Table
CREATE TABLE lead_sources (
    id SERIAL PRIMARY KEY,
    source_name VARCHAR(255) NOT NULL,
    source_code VARCHAR(100) UNIQUE,
    source_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 54. Tags Master Table
CREATE TABLE tags (
    id SERIAL PRIMARY KEY,
    tag_name VARCHAR(255) NOT NULL,
    tag_code VARCHAR(100) UNIQUE,
    tag_color VARCHAR(20),
    tag_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 55. Sales Persons Master Table
CREATE TABLE sales_persons (
    id SERIAL PRIMARY KEY,
    person_name VARCHAR(255) NOT NULL,
    person_code VARCHAR(100) UNIQUE,
    email VARCHAR(255),
    phone VARCHAR(20),
    territory VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 56. Blacklist Status Master Table
CREATE TABLE blacklist_status (
    id SERIAL PRIMARY KEY,
    status_name VARCHAR(100) NOT NULL,
    status_code VARCHAR(50) UNIQUE,
    status_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- LEAD MANAGEMENT TABLES
-- =====================================================

-- 57. Lead Details Table
CREATE TABLE lead_details (
    id SERIAL PRIMARY KEY,
    company VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(20),
    contact VARCHAR(255),
    priority_id INT REFERENCES lead_priorities(id) ON DELETE SET NULL,
    lead_source_id INT REFERENCES lead_sources(id) ON DELETE SET NULL,
    tags VARCHAR(500),
    opportunity VARCHAR(255),
    expected_revenue DECIMAL(15,2),
    sales_person_id INT REFERENCES sales_persons(id) ON DELETE SET NULL,
    blacklist_lead_id INT REFERENCES blacklist_status(id) ON DELETE SET NULL,
    reason TEXT,
    lead_status VARCHAR(100) DEFAULT 'NEW',
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);

-- 58. Lead Tag Mapping Table
CREATE TABLE lead_tag_mapping (
    id SERIAL PRIMARY KEY,
    lead_id INT REFERENCES lead_details(id) ON DELETE CASCADE,
    tag_id INT REFERENCES tags(id) ON DELETE CASCADE,
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(lead_id, tag_id)
);
