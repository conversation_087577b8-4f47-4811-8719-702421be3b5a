erDiagram
    %% Core User Management
    user_details {
        int id PK
        varchar employee_code UK
        varchar first_name
        varchar last_name
        varchar email_id UK
        int designation_id FK
        varchar entity
        varchar role
        boolean is_super_user
        boolean is_active
        timestamp created_date
        timestamp modified_date
        int created_by FK
        int modified_by FK
    }

    designations {
        int id PK
        varchar designation_name
        varchar designation_code UK
        text designation_description
        boolean is_active
    }

    role_details {
        int id PK
        varchar role_id UK
        varchar role_name
        boolean role_status
        text role_description
        varchar select_entity
        boolean is_active
        timestamp created_date
        timestamp modified_date
        int created_by FK
        int modified_by FK
    }

    %% Master Management System
    master_management {
        int id PK
        int master_management_id FK
        varchar master_name
        varchar field_name
        varchar field_type
        int field_type_id FK
        boolean is_mandatory
        boolean is_active
        timestamp created_date
        timestamp modified_date
        int created_by FK
        int modified_by FK
    }

    field_types {
        int id PK
        varchar field_type_name
        text field_type_desc
        varchar display_name
        text help_text
        boolean is_active
    }

    %% Geographic Data
    country_regions {
        int id PK
        varchar region_name
        varchar region_code UK
        varchar country_name
        varchar country_code
        boolean is_active
    }

    states {
        int id PK
        varchar state_name
        varchar state_code UK
        int country_region_id FK
        boolean is_active
    }

    cities {
        int id PK
        varchar city_name
        varchar city_code UK
        int state_id FK
        int country_region_id FK
        boolean is_active
    }

    airports {
        int id PK
        varchar airport_name
        varchar airport_code UK
        varchar iata_code
        varchar icao_code
        int city_id FK
        int country_region_id FK
        boolean is_active
    }

    terminals {
        int id PK
        varchar terminal_name
        varchar terminal_code UK
        int airport_id FK
        boolean is_active
    }

    %% Points and Routes
    points {
        int id PK
        varchar point_id UK
        int country_region_id_1 FK
        int country_region_id_2 FK
        int country_region_id_3 FK
        int country_region_id_4 FK
        boolean is_active
        timestamp created_date
        timestamp modified_date
        int created_by FK
        int modified_by FK
    }

    routes {
        int id PK
        varchar route_name
        int reference_from_airport_record_id FK
        int reference_to_airport_record_id FK
        boolean is_active
        timestamp created_date
        timestamp modified_date
        int created_by FK
        int modified_by FK
    }

    %% Customer and Vendor Management
    customers {
        int id PK
        varchar customer_name
        varchar customer_code UK
        varchar email
        varchar phone
        text address
        int city_id FK
        int country_region_id FK
        boolean is_active
    }

    vendors {
        int id PK
        varchar vendor_name
        varchar vendor_code UK
        varchar email
        varchar phone
        text address
        int city_id FK
        int country_region_id FK
        boolean is_active
    }

    companies {
        int id PK
        varchar company_name
        varchar company_code UK
        varchar email
        varchar phone
        text address
        boolean is_active
    }

    %% Service Types and Conflicts
    service_types {
        int id PK
        varchar service_type_name
        varchar service_type_code UK
        text service_description
        boolean is_active
    }

    customer_conflicts {
        int id PK
        text address
        varchar city_name
        int city_id FK
        int country_region_id FK
        varchar state
        int state_id FK
        int ref_airport_rec_id FK
        int ref_terminal_rec_id FK
        varchar service_type
        int service_type_id FK
        int city_rec_id FK
        int ref_address_region_rec_id FK
        int ref_customer_rec_id FK
        int ref_vendor_rec_id FK
        boolean is_active
        timestamp created_date
        timestamp modified_date
        int created_by FK
        int modified_by FK
    }

    %% Offers and Tenders
    user_types {
        int id PK
        varchar user_type_name
        varchar user_type_code UK
        boolean is_active
    }

    offers {
        int id PK
        varchar offer_title
        varchar upload_file
        text description
        date start_date
        date expiry_date
        int users_type_id FK
        boolean is_active
        timestamp created_date
        timestamp modified_date
        int created_by FK
        int modified_by FK
    }

    tender_types {
        int id PK
        varchar tender_type_name
        varchar tender_type_code UK
        boolean is_active
    }

    fuel_types {
        int id PK
        varchar fuel_type_name
        varchar fuel_type_code UK
        boolean is_active
    }

    bid_status {
        int id PK
        varchar status_name
        varchar status_code UK
        boolean is_active
    }

    incumbents {
        int id PK
        varchar incumbent_name
        varchar incumbent_code UK
        boolean is_active
    }

    tenders {
        int id PK
        varchar tender_doc
        varchar project_tender_id
        int tender_type_id FK
        int company_id FK
        varchar customer_id
        int customer_ref_id FK
        int fuel_type_id FK
        int no_of_rounds
        int bid_status_id FK
        int incumbent_id FK
        date bid_deadline
        boolean is_active
        timestamp created_date
        timestamp modified_date
        int created_by FK
        int modified_by FK
    }

    %% Tickets System
    ticket_types {
        int id PK
        varchar ticket_type_name
        varchar ticket_type_code UK
        boolean is_active
    }

    item_types {
        int id PK
        varchar item_type_name
        varchar item_type_code UK
        boolean is_active
    }

    ticket_priorities {
        int id PK
        varchar priority_name
        varchar priority_code UK
        int priority_level
        boolean is_active
    }

    tickets {
        int id PK
        varchar subject
        text description
        int ticket_type_id FK
        varchar item_type
        int item_type_id FK
        varchar upload_file
        int ticket_priority_id FK
        varchar customer_vendor_name
        int customer_id FK
        int vendor_id FK
        boolean is_active
        timestamp created_date
        timestamp modified_date
        int created_by FK
        int modified_by FK
    }

    %% Lead Management
    lead_priorities {
        int id PK
        varchar priority_name
        varchar priority_code UK
        int priority_level
        boolean is_active
    }

    lead_sources {
        int id PK
        varchar source_name
        varchar source_code UK
        text source_description
        boolean is_active
    }

    sales_persons {
        int id PK
        varchar person_name
        varchar person_code UK
        varchar email
        varchar phone
        varchar territory
        boolean is_active
    }

    blacklist_status {
        int id PK
        varchar status_name
        varchar status_code UK
        text status_description
        boolean is_active
    }

    lead_details {
        int id PK
        varchar company
        varchar email
        varchar phone
        varchar contact
        int priority_id FK
        int lead_source_id FK
        varchar tags
        varchar opportunity
        decimal expected_revenue
        int sales_person_id FK
        int blacklist_lead_id FK
        text reason
        varchar lead_status
        boolean is_active
        timestamp created_date
        timestamp modified_date
        int created_by FK
        int modified_by FK
    }

    %% Activities
    activity_types {
        int id PK
        varchar activity_type_name
        varchar activity_type_code UK
        text activity_description
        boolean is_active
    }

    responsible_persons {
        int id PK
        varchar person_name
        varchar person_code UK
        varchar email
        varchar phone
        varchar department
        boolean is_active
    }

    activities {
        int id PK
        varchar opportunity
        varchar contact_number
        varchar email
        varchar responsible
        int responsible_id FK
        decimal expected_revenue
        date due_date
        varchar activity_type
        int activity_type_id FK
        varchar summary
        varchar activity_status
        varchar priority
        text description
        boolean is_active
        timestamp created_date
        timestamp modified_date
        int created_by FK
        int modified_by FK
    }

    %% Form Management
    form_popups {
        int id PK
        int form_id UK
        varchar form_name
        boolean is_active
        timestamp created_date
        timestamp modified_date
        int created_by FK
        int modified_by FK
    }

    %% Relationships
    user_details ||--o{ user_details : "created_by/modified_by"
    user_details }o--|| designations : "designation_id"
    user_details ||--o{ role_details : "created_by/modified_by"

    master_management }o--|| field_types : "field_type_id"
    master_management }o--|| master_management : "master_management_id"
    master_management }o--|| user_details : "created_by/modified_by"

    states }o--|| country_regions : "country_region_id"
    cities }o--|| states : "state_id"
    cities }o--|| country_regions : "country_region_id"
    airports }o--|| cities : "city_id"
    airports }o--|| country_regions : "country_region_id"
    terminals }o--|| airports : "airport_id"

    points }o--|| country_regions : "country_region_id_1"
    points }o--|| country_regions : "country_region_id_2"
    points }o--|| country_regions : "country_region_id_3"
    points }o--|| country_regions : "country_region_id_4"
    points }o--|| user_details : "created_by/modified_by"

    routes }o--|| airports : "reference_from_airport_record_id"
    routes }o--|| airports : "reference_to_airport_record_id"
    routes }o--|| user_details : "created_by/modified_by"

    customers }o--|| cities : "city_id"
    customers }o--|| country_regions : "country_region_id"
    vendors }o--|| cities : "city_id"
    vendors }o--|| country_regions : "country_region_id"

    customer_conflicts }o--|| cities : "city_id"
    customer_conflicts }o--|| country_regions : "country_region_id"
    customer_conflicts }o--|| states : "state_id"
    customer_conflicts }o--|| airports : "ref_airport_rec_id"
    customer_conflicts }o--|| terminals : "ref_terminal_rec_id"
    customer_conflicts }o--|| service_types : "service_type_id"
    customer_conflicts }o--|| customers : "ref_customer_rec_id"
    customer_conflicts }o--|| vendors : "ref_vendor_rec_id"
    customer_conflicts }o--|| user_details : "created_by/modified_by"

    offers }o--|| user_types : "users_type_id"
    offers }o--|| user_details : "created_by/modified_by"

    tenders }o--|| tender_types : "tender_type_id"
    tenders }o--|| companies : "company_id"
    tenders }o--|| customers : "customer_ref_id"
    tenders }o--|| fuel_types : "fuel_type_id"
    tenders }o--|| bid_status : "bid_status_id"
    tenders }o--|| incumbents : "incumbent_id"
    tenders }o--|| user_details : "created_by/modified_by"

    tickets }o--|| ticket_types : "ticket_type_id"
    tickets }o--|| item_types : "item_type_id"
    tickets }o--|| ticket_priorities : "ticket_priority_id"
    tickets }o--|| customers : "customer_id"
    tickets }o--|| vendors : "vendor_id"
    tickets }o--|| user_details : "created_by/modified_by"

    lead_details }o--|| lead_priorities : "priority_id"
    lead_details }o--|| lead_sources : "lead_source_id"
    lead_details }o--|| sales_persons : "sales_person_id"
    lead_details }o--|| blacklist_status : "blacklist_lead_id"
    lead_details }o--|| user_details : "created_by/modified_by"

    activities }o--|| activity_types : "activity_type_id"
    activities }o--|| responsible_persons : "responsible_id"
    activities }o--|| user_details : "created_by/modified_by"

    form_popups }o--|| user_details : "created_by/modified_by"
