# Complete JSON Database Structure

## Form Configuration Tables

### 1. Collections Table
```sql
CREATE TABLE collections (
    id SERIAL PRIMARY KEY,
    collection_name VARCHAR(255) NOT NULL,
    collection_desc VARCHAR(500),
    collection_api_id VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. Field Types Table
```sql
CREATE TABLE field_types (
    id SERIAL PRIMARY KEY,
    field_type_name VARCHAR(100) NOT NULL,
    field_type_desc VARCHAR(500),
    display_name VARCHAR(100) NOT NULL,
    help_text VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. Components Table
```sql
CREATE TABLE components (
    id SERIAL PRIMARY KEY,
    component_name VA<PERSON>HAR(255) NOT NULL,
    component_display_name VARCHAR(255) NOT NULL,
    component_api_id VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    get_url VARCHAR(500),
    post_url VARCHAR(500),
    update_url VARCHAR(500),
    additional_information TEXT,
    additional_info_image VARCHAR(500),
    parent_component_id INT REFERENCES components(id) ON DELETE SET NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4. Collection Components Table
```sql
CREATE TABLE collection_components (
    id SERIAL PRIMARY KEY,
    collection_id INT REFERENCES collections(id) ON DELETE CASCADE,
    component_id INT REFERENCES components(id) ON DELETE CASCADE,
    display_preference INT NOT NULL,
    is_repeatable BOOLEAN DEFAULT FALSE,
    min_repeat_occurrences INT,
    max_repeat_occurrences INT,
    is_active BOOLEAN DEFAULT TRUE,
    name VARCHAR(255),
    display_name VARCHAR(255),
    additional_info TEXT,
    additional_info_image VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 5. Fields Table
```sql
CREATE TABLE fields (
    id SERIAL PRIMARY KEY,
    field_type_id INT REFERENCES field_types(id) ON DELETE RESTRICT,
    component_id INT REFERENCES components(id) ON DELETE CASCADE,
    collection_id INT REFERENCES collections(id) ON DELETE CASCADE,
    display_preference INT NOT NULL,
    field_name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 6. Field Properties Table
```sql
CREATE TABLE field_properties (
    id SERIAL PRIMARY KEY,
    field_id INT REFERENCES fields(id) ON DELETE CASCADE,
    property_name VARCHAR(100) NOT NULL,
    property_value TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 7. Field Validations Table
```sql
CREATE TABLE field_validations (
    id SERIAL PRIMARY KEY,
    field_id INT REFERENCES fields(id) ON DELETE CASCADE,
    validation_type VARCHAR(100) NOT NULL,
    validation_value TEXT,
    error_message VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Master Data Tables

### 8. Designations Master Table
```sql
CREATE TABLE designations (
    id SERIAL PRIMARY KEY,
    designation_name VARCHAR(100) NOT NULL,
    designation_code VARCHAR(50) UNIQUE,
    designation_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 9. Entities Master Table
```sql
CREATE TABLE entities (
    id SERIAL PRIMARY KEY,
    entity_name VARCHAR(255) NOT NULL,
    entity_code VARCHAR(100) UNIQUE,
    entity_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 10. Roles Master Table
```sql
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    role_name VARCHAR(255) NOT NULL,
    role_code VARCHAR(100) UNIQUE,
    role_description TEXT,
    permissions TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## User Data Tables

### 11. User Details Table
```sql
CREATE TABLE user_details (
    id SERIAL PRIMARY KEY,
    employee_code VARCHAR(100) UNIQUE,
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    email_id VARCHAR(255) UNIQUE NOT NULL,
    designation_id INT REFERENCES designations(id) ON DELETE SET NULL,
    is_super_user BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);
```

### 12. User Entity Mapping Table
```sql
CREATE TABLE user_entity_mapping (
    id SERIAL PRIMARY KEY,
    user_id INT REFERENCES user_details(id) ON DELETE CASCADE,
    entity_id INT REFERENCES entities(id) ON DELETE CASCADE,
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(user_id, entity_id)
);
```

### 13. User Role Mapping Table
```sql
CREATE TABLE user_role_mapping (
    id SERIAL PRIMARY KEY,
    user_id INT REFERENCES user_details(id) ON DELETE CASCADE,
    role_id INT REFERENCES roles(id) ON DELETE CASCADE,
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(user_id, role_id)
);
```

## Role Details Tables

### 14. Role Details Table
```sql
CREATE TABLE role_details (
    id SERIAL PRIMARY KEY,
    role_id VARCHAR(100) UNIQUE,
    role_name VARCHAR(255) NOT NULL,
    role_status BOOLEAN DEFAULT TRUE,
    role_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);
```

### 15. Role Entity Mapping Table
```sql
CREATE TABLE role_entity_mapping (
    id SERIAL PRIMARY KEY,
    role_detail_id INT REFERENCES role_details(id) ON DELETE CASCADE,
    entity_id INT REFERENCES entities(id) ON DELETE CASCADE,
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(role_detail_id, entity_id)
);
```

## Master Management Tables

### 16. Master Management Table
```sql
CREATE TABLE master_management (
    id SERIAL PRIMARY KEY,
    master_name VARCHAR(255) NOT NULL,
    master_code VARCHAR(100) UNIQUE,
    master_type VARCHAR(100),
    master_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);
```

## Point Management Tables

### 17. Points Table
```sql
CREATE TABLE points (
    id SERIAL PRIMARY KEY,
    point_id VARCHAR(100) UNIQUE,
    country_region_id_1 VARCHAR(100),
    country_region_id_2 VARCHAR(100),
    country_region_id_3 VARCHAR(100),
    point_name VARCHAR(255),
    point_description TEXT,
    coordinates VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);
```

### 18. Country Regions Table
```sql
CREATE TABLE country_regions (
    id SERIAL PRIMARY KEY,
    region_code VARCHAR(100) UNIQUE NOT NULL,
    region_name VARCHAR(255) NOT NULL,
    country_code VARCHAR(10),
    country_name VARCHAR(255),
    region_type VARCHAR(50),
    parent_region_id INT REFERENCES country_regions(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```
