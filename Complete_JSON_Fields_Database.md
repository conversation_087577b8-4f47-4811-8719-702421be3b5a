# Complete Database Structure - All JSON Fields

## Form Configuration Tables

### 1. Collections Table
```sql
CREATE TABLE collections (
    id SERIAL PRIMARY KEY,
    collection_name VARCHAR(255) NOT NULL,
    collection_desc VARCHAR(500),
    collection_api_id VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. Field Types Table
```sql
CREATE TABLE field_types (
    id SERIAL PRIMARY KEY,
    field_type_name VARCHAR(100) NOT NULL,
    field_type_desc VARCHAR(500),
    display_name VARCHAR(100) NOT NULL,
    help_text VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. Components Table
```sql
CREATE TABLE components (
    id SERIAL PRIMARY KEY,
    component_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    component_display_name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    component_api_id VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    get_url VARCHAR(500),
    post_url VARCHAR(500),
    update_url VARCHAR(500),
    additional_information TEXT,
    additional_info_image VARCHAR(500),
    parent_component_id INT REFERENCES components(id) ON DELETE SET NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4. Collection Components Table
```sql
CREATE TABLE collection_components (
    id SERIAL PRIMARY KEY,
    collection_id INT REFERENCES collections(id) ON DELETE CASCADE,
    component_id INT REFERENCES components(id) ON DELETE CASCADE,
    display_preference INT NOT NULL,
    is_repeatable BOOLEAN DEFAULT FALSE,
    min_repeat_occurrences INT,
    max_repeat_occurrences INT,
    is_active BOOLEAN DEFAULT TRUE,
    name VARCHAR(255),
    display_name VARCHAR(255),
    additional_info TEXT,
    additional_info_image VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 5. Fields Table
```sql
CREATE TABLE fields (
    id SERIAL PRIMARY KEY,
    field_type_id INT REFERENCES field_types(id) ON DELETE RESTRICT,
    component_id INT REFERENCES components(id) ON DELETE CASCADE,
    collection_id INT REFERENCES collections(id) ON DELETE CASCADE,
    display_preference INT NOT NULL,
    field_name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 6. Field Properties Table
```sql
CREATE TABLE field_properties (
    id SERIAL PRIMARY KEY,
    field_id INT REFERENCES fields(id) ON DELETE CASCADE,
    property_name VARCHAR(100) NOT NULL,
    property_value TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 7. Field Validations Table
```sql
CREATE TABLE field_validations (
    id SERIAL PRIMARY KEY,
    field_id INT REFERENCES fields(id) ON DELETE CASCADE,
    validation_type VARCHAR(100) NOT NULL,
    validation_value TEXT,
    error_message VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Master Data Tables

### 8. Designations Master Table
```sql
CREATE TABLE designations (
    id SERIAL PRIMARY KEY,
    designation_name VARCHAR(100) NOT NULL,
    designation_code VARCHAR(50) UNIQUE,
    designation_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 9. Entities Master Table
```sql
CREATE TABLE entities (
    id SERIAL PRIMARY KEY,
    entity_name VARCHAR(255) NOT NULL,
    entity_code VARCHAR(100) UNIQUE,
    entity_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 10. Roles Master Table
```sql
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    role_name VARCHAR(255) NOT NULL,
    role_code VARCHAR(100) UNIQUE,
    role_description TEXT,
    permissions TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 11. Country Regions Master Table
```sql
CREATE TABLE country_regions (
    id SERIAL PRIMARY KEY,
    region_code VARCHAR(100) UNIQUE NOT NULL,
    region_name VARCHAR(255) NOT NULL,
    country_code VARCHAR(10),
    country_name VARCHAR(255),
    region_type VARCHAR(50),
    parent_region_id INT REFERENCES country_regions(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## User Data Tables

### 12. User Details Table
```sql
CREATE TABLE user_details (
    id SERIAL PRIMARY KEY,
    employee_code VARCHAR(100) UNIQUE,
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    email_id VARCHAR(255) UNIQUE NOT NULL,
    designation_id INT REFERENCES designations(id) ON DELETE SET NULL,
    is_super_user BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);
```

### 13. User Entity Mapping Table
```sql
CREATE TABLE user_entity_mapping (
    id SERIAL PRIMARY KEY,
    user_id INT REFERENCES user_details(id) ON DELETE CASCADE,
    entity_id INT REFERENCES entities(id) ON DELETE CASCADE,
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(user_id, entity_id)
);
```

### 14. User Role Mapping Table
```sql
CREATE TABLE user_role_mapping (
    id SERIAL PRIMARY KEY,
    user_id INT REFERENCES user_details(id) ON DELETE CASCADE,
    role_id INT REFERENCES roles(id) ON DELETE CASCADE,
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(user_id, role_id)
);
```

## Role Management Tables

### 15. Role Details Table
```sql
CREATE TABLE role_details (
    id SERIAL PRIMARY KEY,
    role_id VARCHAR(100) UNIQUE,
    role_name VARCHAR(255) NOT NULL,
    role_status BOOLEAN DEFAULT TRUE,
    role_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);
```

### 16. Role Entity Mapping Table
```sql
CREATE TABLE role_entity_mapping (
    id SERIAL PRIMARY KEY,
    role_detail_id INT REFERENCES role_details(id) ON DELETE CASCADE,
    entity_id INT REFERENCES entities(id) ON DELETE CASCADE,
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(role_detail_id, entity_id)
);
```

## Master Management Tables

### 17. Master Management Table
```sql
CREATE TABLE master_management (
    id SERIAL PRIMARY KEY,
    master_name VARCHAR(255) NOT NULL,
    master_code VARCHAR(100) UNIQUE,
    master_type VARCHAR(100),
    master_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);
```

## Point Management Tables

### 18. Points Table
```sql
CREATE TABLE points (
    id SERIAL PRIMARY KEY,
    point_id VARCHAR(100) UNIQUE,
    country_region_id_1 INT REFERENCES country_regions(id) ON DELETE SET NULL,
    country_region_id_2 INT REFERENCES country_regions(id) ON DELETE SET NULL,
    country_region_id_3 INT REFERENCES country_regions(id) ON DELETE SET NULL,
    country_region_id_4 INT REFERENCES country_regions(id) ON DELETE SET NULL,
    point_name VARCHAR(255),
    point_description TEXT,
    coordinates VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);
```

## Additional Master Data Tables

### 19. Cities Master Table
```sql
CREATE TABLE cities (
    id SERIAL PRIMARY KEY,
    city_name VARCHAR(255) NOT NULL,
    city_code VARCHAR(100) UNIQUE,
    state_id INT REFERENCES states(id) ON DELETE SET NULL,
    country_region_id INT REFERENCES country_regions(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 20. States Master Table
```sql
CREATE TABLE states (
    id SERIAL PRIMARY KEY,
    state_name VARCHAR(255) NOT NULL,
    state_code VARCHAR(100) UNIQUE,
    country_region_id INT REFERENCES country_regions(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 21. Airports Master Table
```sql
CREATE TABLE airports (
    id SERIAL PRIMARY KEY,
    airport_name VARCHAR(255) NOT NULL,
    airport_code VARCHAR(10) UNIQUE,
    iata_code VARCHAR(3),
    icao_code VARCHAR(4),
    city_id INT REFERENCES cities(id) ON DELETE SET NULL,
    country_region_id INT REFERENCES country_regions(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 22. Terminals Master Table
```sql
CREATE TABLE terminals (
    id SERIAL PRIMARY KEY,
    terminal_name VARCHAR(255) NOT NULL,
    terminal_code VARCHAR(50) UNIQUE,
    airport_id INT REFERENCES airports(id) ON DELETE CASCADE,
    terminal_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 23. Service Types Master Table
```sql
CREATE TABLE service_types (
    id SERIAL PRIMARY KEY,
    service_type_name VARCHAR(255) NOT NULL,
    service_type_code VARCHAR(100) UNIQUE,
    service_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 24. Customers Master Table
```sql
CREATE TABLE customers (
    id SERIAL PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_code VARCHAR(100) UNIQUE,
    customer_type VARCHAR(100),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 25. Vendors Master Table
```sql
CREATE TABLE vendors (
    id SERIAL PRIMARY KEY,
    vendor_name VARCHAR(255) NOT NULL,
    vendor_code VARCHAR(100) UNIQUE,
    vendor_type VARCHAR(100),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Customer Conflict Management Tables

### 26. Customer Conflicts Table
```sql
CREATE TABLE customer_conflicts (
    id SERIAL PRIMARY KEY,
    address TEXT,
    city_name VARCHAR(255),
    city_id INT REFERENCES cities(id) ON DELETE SET NULL,
    country_region_id INT REFERENCES country_regions(id) ON DELETE SET NULL,
    state VARCHAR(255),
    state_id INT REFERENCES states(id) ON DELETE SET NULL,
    ref_airport_rec_id INT REFERENCES airports(id) ON DELETE SET NULL,
    ref_terminal_rec_id INT REFERENCES terminals(id) ON DELETE SET NULL,
    service_type VARCHAR(255),
    service_type_id INT REFERENCES service_types(id) ON DELETE SET NULL,
    city_rec_id INT REFERENCES cities(id) ON DELETE SET NULL,
    ref_address_region_rec_id INT REFERENCES country_regions(id) ON DELETE SET NULL,
    ref_customer_rec_id INT REFERENCES customers(id) ON DELETE SET NULL,
    ref_vendor_rec_id INT REFERENCES vendors(id) ON DELETE SET NULL,
    conflict_status VARCHAR(100) DEFAULT 'OPEN',
    conflict_description TEXT,
    resolution_notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);
```

## Route Management Tables

### 27. Routes Table
```sql
CREATE TABLE routes (
    id SERIAL PRIMARY KEY,
    route_name VARCHAR(255) NOT NULL,
    reference_from_airport_record_id INT REFERENCES airports(id) ON DELETE SET NULL,
    reference_to_airport_record_id INT REFERENCES airports(id) ON DELETE SET NULL,
    route_code VARCHAR(100) UNIQUE,
    distance_km DECIMAL(10,2),
    estimated_duration_minutes INT,
    route_status VARCHAR(50) DEFAULT 'ACTIVE',
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);
```

## User Types Master Table

### 28. User Types Master Table
```sql
CREATE TABLE user_types (
    id SERIAL PRIMARY KEY,
    user_type_name VARCHAR(100) NOT NULL,
    user_type_code VARCHAR(50) UNIQUE,
    user_type_description TEXT,
    permissions TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Offer Management Tables

### 29. Offers Table
```sql
CREATE TABLE offers (
    id SERIAL PRIMARY KEY,
    offer_title VARCHAR(255) NOT NULL,
    upload_file VARCHAR(500),
    file_name VARCHAR(255),
    file_size BIGINT,
    file_type VARCHAR(100),
    description TEXT NOT NULL,
    start_date DATE,
    expiry_date DATE,
    users_type_id INT REFERENCES user_types(id) ON DELETE SET NULL,
    offer_status VARCHAR(50) DEFAULT 'ACTIVE',
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);
```

## File Management Table

### 30. File Uploads Table
```sql
CREATE TABLE file_uploads (
    id SERIAL PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    original_file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    file_type VARCHAR(100),
    mime_type VARCHAR(100),
    table_name VARCHAR(100),
    record_id INT,
    field_name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    uploaded_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    uploaded_by INT REFERENCES user_details(id) ON DELETE SET NULL
);
```
