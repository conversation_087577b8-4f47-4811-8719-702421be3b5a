-- Dynamic Form Configuration System - Complete SQL Script
-- Database: PostgreSQL / SQL Server Compatible

-- Create Database (PostgreSQL)
-- CREATE DATABASE dynamic_form_configuration;

-- Create Database (SQL Server)
-- CREATE DATABASE DynamicFormConfiguration;

-- =====================================================
-- TABLE CREATION SCRIPTS
-- =====================================================

-- 1. Collections Table
CREATE TABLE collections (
    id SERIAL PRIMARY KEY,
    collection_name VARCHAR(255) NOT NULL,
    collection_desc VARCHAR(500),
    collection_api_id VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. Field Types Table
CREATE TABLE field_types (
    id SERIAL PRIMARY KEY,
    field_type_name VARCHAR(100) NOT NULL UNIQUE,
    field_type_desc VARCHAR(500),
    display_name VARCHAR(100) NOT NULL,
    help_text VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. Components Table
CREATE TABLE components (
    id SERIAL PRIMARY KEY,
    component_name VARCHAR(255) NOT NULL,
    component_display_name VARCHAR(255) NOT NULL,
    component_api_id VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    get_url VARCHAR(500),
    post_url VARCHAR(500),
    update_url VARCHAR(500),
    additional_information TEXT,
    additional_info_image VARCHAR(500),
    parent_component_id INT REFERENCES components(id),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. Collection Components Table
CREATE TABLE collection_components (
    id SERIAL PRIMARY KEY,
    collection_id INT REFERENCES collections(id) ON DELETE CASCADE,
    component_id INT REFERENCES components(id) ON DELETE CASCADE,
    display_preference INT NOT NULL DEFAULT 0,
    is_repeatable BOOLEAN DEFAULT FALSE,
    min_repeat_occurrences INT,
    max_repeat_occurrences INT,
    is_active BOOLEAN DEFAULT TRUE,
    name VARCHAR(255),
    display_name VARCHAR(255),
    additional_info TEXT,
    additional_info_image VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(collection_id, display_preference)
);

-- 5. Fields Table
CREATE TABLE fields (
    id SERIAL PRIMARY KEY,
    field_type_id INT REFERENCES field_types(id),
    component_id INT REFERENCES components(id) ON DELETE CASCADE,
    collection_id INT REFERENCES collections(id) ON DELETE CASCADE,
    display_preference INT NOT NULL DEFAULT 0,
    field_name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CHECK ((component_id IS NOT NULL AND collection_id IS NULL) OR 
           (component_id IS NULL AND collection_id IS NOT NULL))
);

-- 6. Field Properties Table
CREATE TABLE field_properties (
    id SERIAL PRIMARY KEY,
    field_id INT REFERENCES fields(id) ON DELETE CASCADE,
    property_name VARCHAR(100) NOT NULL,
    property_value TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(field_id, property_name)
);

-- 7. Field Validations Table
CREATE TABLE field_validations (
    id SERIAL PRIMARY KEY,
    field_id INT REFERENCES fields(id) ON DELETE CASCADE,
    validation_type VARCHAR(100) NOT NULL,
    validation_value TEXT,
    error_message VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Collections Indexes
CREATE INDEX idx_collections_api_id ON collections(collection_api_id);
CREATE INDEX idx_collections_active ON collections(is_active);
CREATE INDEX idx_collections_name ON collections(collection_name);

-- Field Types Indexes
CREATE INDEX idx_field_types_name ON field_types(field_type_name);
CREATE INDEX idx_field_types_active ON field_types(is_active);

-- Components Indexes
CREATE INDEX idx_components_api_id ON components(component_api_id);
CREATE INDEX idx_components_active ON components(is_active);
CREATE INDEX idx_components_parent ON components(parent_component_id);
CREATE INDEX idx_components_name ON components(component_name);

-- Collection Components Indexes
CREATE INDEX idx_collection_components_collection ON collection_components(collection_id);
CREATE INDEX idx_collection_components_component ON collection_components(component_id);
CREATE INDEX idx_collection_components_order ON collection_components(display_preference);
CREATE INDEX idx_collection_components_active ON collection_components(is_active);

-- Fields Indexes
CREATE INDEX idx_fields_component ON fields(component_id);
CREATE INDEX idx_fields_collection ON fields(collection_id);
CREATE INDEX idx_fields_field_type ON fields(field_type_id);
CREATE INDEX idx_fields_display_order ON fields(display_preference);
CREATE INDEX idx_fields_active ON fields(is_active);
CREATE INDEX idx_fields_name ON fields(field_name);

-- Field Properties Indexes
CREATE INDEX idx_field_properties_field ON field_properties(field_id);
CREATE INDEX idx_field_properties_name ON field_properties(property_name);

-- Field Validations Indexes
CREATE INDEX idx_field_validations_field ON field_validations(field_id);
CREATE INDEX idx_field_validations_type ON field_validations(validation_type);

-- =====================================================
-- SAMPLE DATA INSERTION
-- =====================================================

-- Insert Field Types
INSERT INTO field_types (field_type_name, field_type_desc, display_name, help_text, is_active) VALUES
('text', 'Simple text field', 'Text', 'Enter text here', TRUE),
('dropdown', 'Dropdown selection', 'Dropdown', 'Select from dropdown', TRUE),
('multi_select', 'Multi-select field', 'Multi-Select', 'Select multiple options', TRUE),
('checkbox', 'Checkbox input field', 'Checkbox', 'Select one or more options', TRUE),
('input_switch', 'Toggle switch input', 'InputSwitch', 'Toggle the option on or off', TRUE),
('textarea', 'Multi-line text input', 'Textarea', 'Enter multi-line text', TRUE);

-- Insert Sample Collections
INSERT INTO collections (collection_name, collection_desc, collection_api_id) VALUES
('Add User', 'Add User collection', 'aviation_services_management_asm_admin_add_user_'),
('Role_Details_Add', 'Role_Details_Add collection', 'aviation_services_management_asm_admin_role_details_add'),
('Form Popup', 'Form Popup collection', 'aviation_services_management_asm_admin_form_popup'),
('Master_Management_Add', 'Master_Management_Add collection', 'master_management_add'),
('Add Point', 'Add Point collection', 'aviation_services_management_asm_admin_add_point');

-- Insert Sample Components
INSERT INTO components (component_name, component_display_name, component_api_id, is_active) VALUES
('User Details', 'User Details', 'user_details', TRUE);

-- Insert Collection Components Relationship
INSERT INTO collection_components (collection_id, component_id, display_preference, is_repeatable, name, display_name) VALUES
(1, 1, 10, FALSE, 'User Details', 'User Details');

-- Insert Sample Fields for User Details Component
INSERT INTO fields (field_type_id, component_id, display_preference, field_name, display_name) VALUES
(1, 1, 10, 'Employee_Code', 'Employee Code'),
(1, 1, 20, 'First_Name', 'First Name'),
(1, 1, 30, 'Last_Name', 'Last Name'),
(1, 1, 40, 'Email_Id', 'Email Id'),
(2, 1, 50, 'Designation', 'Designation'),
(3, 1, 70, 'Entity', 'Entity'),
(3, 1, 80, 'Role', 'Role'),
(4, 1, 90, 'is_Super_User', 'is Super User?');

-- Insert Sample Fields for Role_Details_Add Collection (Direct fields)
INSERT INTO fields (field_type_id, collection_id, display_preference, field_name, display_name) VALUES
(1, 2, 10, 'Role_ID', 'Role ID'),
(1, 2, 20, 'Role_Name', 'Role Name'),
(5, 2, 30, 'Role_Status', 'Role Status'),
(3, 2, 40, 'Select_Entity', 'Select Entity'),
(6, 2, 50, 'Role_Description', 'Role Description');

-- Insert Sample Fields for Add Point Collection
INSERT INTO fields (field_type_id, collection_id, display_preference, field_name, display_name) VALUES
(1, 5, 10, 'Point_ID', 'Point ID'),
(1, 5, 20, 'Country_Region_ID_1', 'Country Region ID 1'),
(1, 5, 30, 'Country_Region_ID_2', 'Country Region ID 2'),
(1, 5, 40, 'Country_Region_ID_3', 'Country Region ID 3');

-- Insert Sample Field Properties
INSERT INTO field_properties (field_id, property_name, property_value) VALUES
(1, 'name', 'Employee_Code'),
(1, 'display-name', 'Employee Code'),
(2, 'name', 'First_Name'),
(2, 'display-name', 'First Name'),
(3, 'name', 'Last_Name'),
(3, 'display-name', 'Last Name'),
(4, 'name', 'Email_Id'),
(4, 'display-name', 'Email Id'),
(5, 'name', 'Designation'),
(5, 'display-name', 'Designation');

-- Insert Sample Field Validations
INSERT INTO field_validations (field_id, validation_type, validation_value) VALUES
(4, 'regex', '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}(\\.[a-zA-Z]{2,})?$'),
(1, 'required', 'false'),
(2, 'required', 'false'),
(3, 'required', 'false'),
(4, 'required', 'false');

-- =====================================================
-- USEFUL QUERIES
-- =====================================================

-- Query to get all collections with their components and fields
/*
SELECT 
    c.id as collection_id,
    c.collection_name,
    c.collection_api_id,
    comp.id as component_id,
    comp.component_name,
    comp.component_api_id,
    f.id as field_id,
    f.field_name,
    f.display_name as field_display_name,
    ft.field_type_name,
    f.display_preference
FROM collections c
LEFT JOIN collection_components cc ON c.id = cc.collection_id
LEFT JOIN components comp ON cc.component_id = comp.id
LEFT JOIN fields f ON (comp.id = f.component_id OR c.id = f.collection_id)
LEFT JOIN field_types ft ON f.field_type_id = ft.id
WHERE c.is_active = TRUE
ORDER BY c.id, cc.display_preference, f.display_preference;
*/
