# Dynamic Form Configuration System - Database Schema Documentation

## Overview
This document describes the database schema for a dynamic form configuration system that supports collections, components, and fields with various field types and validation rules.

## Database Schema Design

### 1. Collections Table
Stores form collections (main containers for forms)

```sql
CREATE TABLE collections (
    id SERIAL PRIMARY KEY,
    collection_name VARCHAR(255) NOT NULL,
    collection_desc VARCHAR(500),
    collection_api_id VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. Field Types Table
Defines available field types (text, dropdown, multi_select, etc.)

```sql
CREATE TABLE field_types (
    id SERIAL PRIMARY KEY,
    field_type_name VARCHAR(100) NOT NULL UNIQUE,
    field_type_desc VARCHAR(500),
    display_name VARCHAR(100) NOT NULL,
    help_text VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. Components Table
Stores reusable components that can contain fields

```sql
CREATE TABLE components (
    id SERIAL PRIMARY KEY,
    component_name VARCHAR(255) NOT NULL,
    component_display_name VARCHAR(255) NOT NULL,
    component_api_id VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    get_url VARCHAR(500),
    post_url VARCHAR(500),
    update_url VARCHAR(500),
    additional_information TEXT,
    additional_info_image VARCHAR(500),
    parent_component_id INT REFERENCES components(id),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4. Collection Components Table
Links components to collections with display preferences

```sql
CREATE TABLE collection_components (
    id SERIAL PRIMARY KEY,
    collection_id INT REFERENCES collections(id) ON DELETE CASCADE,
    component_id INT REFERENCES components(id) ON DELETE CASCADE,
    display_preference INT NOT NULL DEFAULT 0,
    is_repeatable BOOLEAN DEFAULT FALSE,
    min_repeat_occurrences INT,
    max_repeat_occurrences INT,
    is_active BOOLEAN DEFAULT TRUE,
    name VARCHAR(255),
    display_name VARCHAR(255),
    additional_info TEXT,
    additional_info_image VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(collection_id, display_preference)
);
```

### 5. Fields Table
Stores individual fields with their configurations

```sql
CREATE TABLE fields (
    id SERIAL PRIMARY KEY,
    field_type_id INT REFERENCES field_types(id),
    component_id INT REFERENCES components(id) ON DELETE CASCADE,
    collection_id INT REFERENCES collections(id) ON DELETE CASCADE,
    display_preference INT NOT NULL DEFAULT 0,
    field_name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CHECK ((component_id IS NOT NULL AND collection_id IS NULL) OR 
           (component_id IS NULL AND collection_id IS NOT NULL))
);
```

### 6. Field Properties Table
Stores field properties (name, display-name, etc.)

```sql
CREATE TABLE field_properties (
    id SERIAL PRIMARY KEY,
    field_id INT REFERENCES fields(id) ON DELETE CASCADE,
    property_name VARCHAR(100) NOT NULL,
    property_value TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(field_id, property_name)
);
```

### 7. Field Validations Table
Stores validation rules for fields

```sql
CREATE TABLE field_validations (
    id SERIAL PRIMARY KEY,
    field_id INT REFERENCES fields(id) ON DELETE CASCADE,
    validation_type VARCHAR(100) NOT NULL,
    validation_value TEXT,
    error_message VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Indexes for Performance

```sql
-- Collections
CREATE INDEX idx_collections_api_id ON collections(collection_api_id);
CREATE INDEX idx_collections_active ON collections(is_active);

-- Components
CREATE INDEX idx_components_api_id ON components(component_api_id);
CREATE INDEX idx_components_active ON components(is_active);
CREATE INDEX idx_components_parent ON components(parent_component_id);

-- Fields
CREATE INDEX idx_fields_component ON fields(component_id);
CREATE INDEX idx_fields_collection ON fields(collection_id);
CREATE INDEX idx_fields_field_type ON fields(field_type_id);
CREATE INDEX idx_fields_display_order ON fields(display_preference);

-- Collection Components
CREATE INDEX idx_collection_components_collection ON collection_components(collection_id);
CREATE INDEX idx_collection_components_component ON collection_components(component_id);
CREATE INDEX idx_collection_components_order ON collection_components(display_preference);

-- Field Properties
CREATE INDEX idx_field_properties_field ON field_properties(field_id);
CREATE INDEX idx_field_properties_name ON field_properties(property_name);

-- Field Validations
CREATE INDEX idx_field_validations_field ON field_validations(field_id);
CREATE INDEX idx_field_validations_type ON field_validations(validation_type);
```

## Sample Data Insertion

### Insert Field Types
```sql
INSERT INTO field_types (field_type_name, field_type_desc, display_name, help_text, is_active) VALUES
('text', 'Simple text field', 'Text', 'Enter text here', TRUE),
('dropdown', 'Dropdown selection', 'Dropdown', 'Select from dropdown', TRUE),
('multi_select', 'Multi-select field', 'Multi-Select', 'Select multiple options', TRUE),
('checkbox', 'Checkbox input field', 'Checkbox', 'Select one or more options', TRUE),
('input_switch', 'Toggle switch input', 'InputSwitch', 'Toggle the option on or off', TRUE),
('textarea', 'Multi-line text input', 'Textarea', 'Enter multi-line text', TRUE);
```

### Insert Sample Collections
```sql
INSERT INTO collections (collection_name, collection_desc, collection_api_id) VALUES
('Add User', 'Add User collection', 'aviation_services_management_asm_admin_add_user_'),
('Role_Details_Add', 'Role_Details_Add collection', 'aviation_services_management_asm_admin_role_details_add'),
('Form Popup', 'Form Popup collection', 'aviation_services_management_asm_admin_form_popup'),
('Master_Management_Add', 'Master_Management_Add collection', 'master_management_add'),
('Add Point', 'Add Point collection', 'aviation_services_management_asm_admin_add_point');
```

### Insert Sample Components
```sql
INSERT INTO components (component_name, component_display_name, component_api_id, is_active) VALUES
('User Details', 'User Details', 'user_details', TRUE);
```

### Insert Collection Components Relationship
```sql
INSERT INTO collection_components (collection_id, component_id, display_preference, is_repeatable, name, display_name) VALUES
(1, 1, 10, FALSE, 'User Details', 'User Details');
```

## Entity Relationships

### Primary Relationships:
1. **collections** → **collection_components** (1:Many)
2. **components** → **collection_components** (1:Many)
3. **components** → **fields** (1:Many)
4. **collections** → **fields** (1:Many) - Direct fields
5. **field_types** → **fields** (1:Many)
6. **fields** → **field_properties** (1:Many)
7. **fields** → **field_validations** (1:Many)
8. **components** → **components** (Self-referencing for child components)

### Key Business Rules:
- A field must belong to either a Component OR a Collection (not both)
- Components can have child components (hierarchical structure)
- Display preferences determine the order of fields/components
- Field properties store dynamic configuration (name, display-name, etc.)
- Field validations store rules like required, regex patterns, etc.
- Collection API IDs must be unique across the system
- Component API IDs must be unique across the system

## Data Types Used:
- **SERIAL**: Auto-incrementing integer primary key
- **INT**: Integer for foreign keys and numeric values
- **VARCHAR(n)**: Variable character string with maximum length
- **TEXT**: Unlimited text for large content
- **BOOLEAN**: True/false values
- **TIMESTAMP**: Date and time values
- **NUMERIC(p,s)**: Decimal numbers with precision and scale

## Constraints:
- **PRIMARY KEY**: Unique identifier for each table
- **REFERENCES**: Foreign key relationships
- **UNIQUE**: Ensures uniqueness of values
- **NOT NULL**: Mandatory fields
- **DEFAULT**: Default values for fields
- **CHECK**: Custom validation constraints
- **ON DELETE CASCADE**: Automatic deletion of related records
