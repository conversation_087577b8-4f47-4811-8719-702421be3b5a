# Dynamic Form Configuration System - Database Schema Documentation

## Overview
This document describes the database schema for a dynamic form configuration system that supports collections, components, and fields with various field types and validation rules.

## Database Schema Design

### 1. Collections Table
Stores form collections (main containers for forms)

```sql
CREATE TABLE collections (
    id S RIALY,
    collection_nnameVARCHAR(255) NOT NULL,
    collection_ddescVARCHAR(500),
    collection_aap_i_i VARCHAR(255) UNIQUE NOT NULL,
    is_aactive OOLEANOLEAN DEFTRUEULT TRUE,
    created_ddateISTASTAMPP DEFAULTCURRCNU_EIMNSTAMPTIMESTAMP,
    modified_ddateISTASTAMPP DEFAULTCURRCNU_EIMNSTAMPTIMESTAMP
);
```

### 2. Field  Types Table
Defines available field types (text, dropdown, multi_select, etc.)

```sql
CREATE TABLE field_ttypes (
    id S RIALY,
    field_ttyp_n_nam VARCHAR(100) NOT NULL UNIQUE,
    field_ttyp_d_des VARCHAR(500),
    display_nnameVARCHAR(100) NOT NULL,
    help_ttextVARCHAR(500),
    is_aactive OOLEANOLEAN DEFTRUEULT TRUE,
    created_ddateISTASTAMPP DEFAULTCURRCNU_EIMNSTAMPTIMESTAMP
);
```

### 3. Components Table
Stores reusable components that can contain fields

```sql
CREATE TABLE components (
    id S RIALY,
    component_nnameVARCHAR(255) NOT NULL,
    component_ddispla_n_nam VARCHAR(255) NOT NULL,
    component_aap_i_i VARCHAR(255) UNIQUE NOT NULL,
    is_aactive OOLEANOLEAN DEFTRUEULT TRUE,
    get_uurlVARCHAR(500),
    post_uurlVARCHAR(500),
    update_uurlVARCHAR(500),
    additional_iinformationTET
    additional_iinf_i_imag VARCHAR(500),
    parent_ccomponen_i_id INREFERETCES components(id)FERENCES components(id),
    created_ddateISTASTAMPP DEFAULTCURRCNU_EIMNSTAMPTIMESTAMP,
    modified_ddateISTASTAMPP DEFAULTCURRCNU_EIMNM
## 4. Collection Components Table
Links components to collections with display preferences

```sql 
CREATE TABLE collection_components (
    id SERIAL PRIMARY KEY,
    collection_id INT REFERENCES collections(id) ON DELETE CASCADE,
    componentcid INT RE_cERENCES components(id) ON DELETE CASCADE,
    iisSrReALNT NOT NULL DEFAULT 0,
    cs_repeata_ile BOOREFERELCESNcollections(id) O  DEEETE CASCADEAULT FALSE,
    cin_repea_i_occurREFERErCES components(id) eN DELEnEcCASCADENT,
    dax_rep_pat_occurrences INT,
    is_ractive BOOLOOLEANN DEFAULTFALSETRUE,
    mam_r VARC_oAR(255),
    mis_rlay_n_ome VARCHAR(255
    id_aitionalOOLEANnfo TEXT,TRUE
    ndditonal_info_image VARCHAR(500),
    dreated_ndateTIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    aNIQUE(col_iectiTElTy_preference)
);a_i_i
```cd_STAMP ilU fTMMP
id SERIAL RcMARY KEY,_id_p
    field_type_id INT REFERENCES field_types(id),
    component_id INT REFERENCES components(id) ON DELETE CASCADE,
    collection_id INT REFERENCES collections(id) ON DELETE CASCADE,
    display_preference INT NOT NULL DEFAULT 0,
    field_name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_dfte TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ioeSdReALAMP DEFAULT CURRENT_TIMESTAMP,
    fHECK_t((c_imponenREFEREtCESdfield_types(id)NOT NULL AND collection_id IS NULL) OR
    c      (c_impone REFERENCES components(id)nOt DEdETE CASCADE IS NULL AND collection_id IS NOT NULL))
);c_iREFERECES collections(id) ON DEETE CASCADE
```d_p
f_n
### d. Fiel_n Proerties Table
Storis_afield pOOLEANperties (TRUEame, display-name, etc.)
c_dSTAMPCURRN_IMSTAMP
```sml_dSTAMPiURidNS _MfMP
);c_ic_i
c_ic_i
### 7. Field Validations Table
Stores validation rules for fields

```sql 
CREATE TABLE field_validations (
    id SERIAL PRIMARY KEY,
    field_id INT REFERENCES fields(id) ON DELETE CASCADE,
    validatiof_typ_p VARCHAR(100) NOT NULL, -- 'required', 'regex', 'min', 'max', etc.
    ialSnRaALT,
    frror_imessagREFEREeCESAfields(id) OR DEHETE CASCADER(500),
    preated__nate IMESTAMP DEFAULT CURRENT_TIMESTAMP
);p_vTET
```c_dSTAMPnURsEorTMM,
ollectionsf_ip_n
CREATE INDEX idx_collections_api_id ON collections(collection_api_id);
CREATE INDEX idx_collections_active ON collections(is_active);

-- Component s
CREATE INDEX idx_components_api_id ON components(component_api_id);
CREATE INDEX idx_components_active ON components(is_active);
CREATE INDEX idx_components_parent ON components(parent_component_id);
f_v
-- FieSRAL
CREAfE IN_iEX idxREFERE_CES fields(id) fN DELEiEeCASCADEcomponent ON fields(component_id);
CREAvE INDEX i_tx_filds_collection ON fields(collection_id);
CREAvE INDEX i_vx_fieTEyTe ON fields(field_type_id);
CREAeE IN_mEX idx_ields_display_order ON fields(display_preference);
cd_STAMPATURDE ieTnM_cipMPents(component_id);
CREATE INDEX idx_collection_components_order ON collection_components(display_preference);
```

## Sample Data Insertion

### Insert Field Types
```sql
INSERT INTO FidxlcTypes (FielaTi_yeNamec FieldTypeDcsc, Displ_ayi_Nme, HelpText, IsActive) VALUES
('text', 'Simidxectext field'a 'Text', cEnter text ie_ae', 1),
('dropdown', 'Dropdown selection', 'Dropdown', 'Select from dropdown', 1),
('multi_select', 'Multi-select field', 'Multi-Select', 'Select multiple options', 1),
('checkbox', idxhcckbox inpua i_feld',c'Checkbox'c 'Select_aoi_n or more options', 1),
('input_switcidx,c'Toggle swatch inputc, 'InputSwit_ah', 'Toggle the option on or off', 1),
('textarea', idxucti-line tept input',c'Textarea'p 'Ent_cr multi-_iine text', 1);
```

## Entity Relidxifnshipscfc_i
idxfcfc_i
### Primary Ridxafionshifs:_tff_t_i
1. **Collectiidxsf* → **dollect_oonComponfnts** d1:Many_p
2. **Components** → **CollectionComponents** (1:Many)
3. **Componen ts** → **Fields** (1:Many)
4. **Collectiidxsc* → **Fie_cds** (1:Macy) - Direct fcelds_cc_i
5. **FieldTypidx*c → **Fiel_cs** (1:Manc)c_cc_i
6. **Fields**idx c*FieldPro_certies** (o:Many)c_cd_p
7. **Fields** → **FieldValidations** (1:Many)
8. **Components** → **Components** (Self-referencing for child components)

### Key Business Rules:
- A field must belong to either a Component OR a Collection (not both)
- Components can have child components (hierarchical structure)
- Display preferences determine the order of fields/components
- Field properties store dynamic configuration (name, display-name, etc.)
- Field validations store rules like required, regex patterns, etc.

## .NET Entity Models

### Collection Entity
```csharp
public class Collection
{
    public int Id { get; set; }
    public string CollectionName { get; set; }
    public string CollectionDesc { get; set; }
    public string CollectionApiId { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    public DateTime ModifiedDate { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    public virtual ICollection<CollectionComponent> CollectionComponents { get; set; }
    public virtual ICollection<Field> Fields { get; set; }
}
```

### Component Entity
```csharp
public class Component
{
    public int Id { get; set; }
    public string ComponentName { get; set; }
    public string ComponentDisplayName { get; set; }
    public string ComponentApiId { get; set; }
    public bool IsActive { get; set; } = true;
    public string GetUrl { get; set; }
    public string PostUrl { get; set; }
    public string UpdateUrl { get; set; }
    public string AdditionalInformation { get; set; }
    public string AdditionalInfoImage { get; set; }
    public int? ParentComponentId { get; set; }
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    public DateTime ModifiedDate { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    public virtual Component ParentComponent { get; set; }
    public virtual ICollection<Component> ChildComponents { get; set; }
    public virtual ICollection<CollectionComponent> CollectionComponents { get; set; }
    public virtual ICollection<Field> Fields { get; set; }
}
```

### FieldType Entity
```csharp
public class FieldType
{
    public int Id { get; set; }
    public string FieldTypeName { get; set; }
    public string FieldTypeDesc { get; set; }
    public string DisplayName { get; set; }
    public string HelpText { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    public virtual ICollection<Field> Fields { get; set; }
}
```

### Field Entity
```csharp
public class Field
{
    public int Id { get; set; }
    public int FieldTypeId { get; set; }
    public int? ComponentId { get; set; }
    public int? CollectionId { get; set; }
    public int DisplayPreference { get; set; }
    public string FieldName { get; set; }
    public string DisplayName { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    public DateTime ModifiedDate { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    public virtual FieldType FieldType { get; set; }
    public virtual Component Component { get; set; }
    public virtual Collection Collection { get; set; }
    public virtual ICollection<FieldProperty> FieldProperties { get; set; }
    public virtual ICollection<FieldValidation> FieldValidations { get; set; }
}
```

### CollectionComponent Entity
```csharp
public class CollectionComponent
{
    public int Id { get; set; }
    public int CollectionId { get; set; }
    public int ComponentId { get; set; }
    public int DisplayPreference { get; set; }
    public bool IsRepeatable { get; set; } = false;
    public int? MinRepeatOccurrences { get; set; }
    public int? MaxRepeatOccurrences { get; set; }
    public bool IsActive { get; set; } = true;
    public string Name { get; set; }
    public string DisplayName { get; set; }
    public string AdditionalInfo { get; set; }
    public string AdditionalInfoImage { get; set; }
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    public virtual Collection Collection { get; set; }
    public virtual Component Component { get; set; }
}
```

### FieldProperty Entity
```csharp
public class FieldProperty
{
    public int Id { get; set; }
    public int FieldId { get; set; }
    public string PropertyName { get; set; }
    public string PropertyValue { get; set; }
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    public virtual Field Field { get; set; }
}
```

### FieldValidation Entity
```csharp
public class FieldValidation
{
    public int Id { get; set; }
    public int FieldId { get; set; }
    public string ValidationType { get; set; }
    public string ValidationValue { get; set; }
    public string ErrorMessage { get; set; }
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    public virtual Field Field { get; set; }
}
```

## DbContext Configuration

```csharp
public class FormConfigurationDbContext : DbContext
{
    public DbSet<Collection> Collections { get; set; }
    public DbSet<Component> Components { get; set; }
    public DbSet<FieldType> FieldTypes { get; set; }
    public DbSet<Field> Fields { get; set; }
    public DbSet<CollectionComponent> CollectionComponents { get; set; }
    public DbSet<FieldProperty> FieldProperties { get; set; }
    public DbSet<FieldValidation> FieldValidations { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Collection Configuration
        modelBuilder.Entity<Collection>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.CollectionName).IsRequired().HasMaxLength(255);
            entity.Property(e => e.CollectionApiId).IsRequired().HasMaxLength(255);
            entity.HasIndex(e => e.CollectionApiId).IsUnique();
        });

        // Component Configuration
        modelBuilder.Entity<Component>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ComponentName).IsRequired().HasMaxLength(255);
            entity.Property(e => e.ComponentApiId).IsRequired().HasMaxLength(255);
            entity.HasIndex(e => e.ComponentApiId).IsUnique();

            entity.HasOne(e => e.ParentComponent)
                  .WithMany(e => e.ChildComponents)
                  .HasForeignKey(e => e.ParentComponentId)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        // Field Configuration
        modelBuilder.Entity<Field>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.FieldName).IsRequired().HasMaxLength(255);
            entity.Property(e => e.DisplayName).IsRequired().HasMaxLength(255);

            entity.HasOne(e => e.FieldType)
                  .WithMany(e => e.Fields)
                  .HasForeignKey(e => e.FieldTypeId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.Component)
                  .WithMany(e => e.Fields)
                  .HasForeignKey(e => e.ComponentId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Collection)
                  .WithMany(e => e.Fields)
                  .HasForeignKey(e => e.CollectionId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // CollectionComponent Configuration
        modelBuilder.Entity<CollectionComponent>(entity =>
        {
            entity.HasKey(e => e.Id);

            entity.HasOne(e => e.Collection)
                  .WithMany(e => e.CollectionComponents)
                  .HasForeignKey(e => e.CollectionId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Component)
                  .WithMany(e => e.CollectionComponents)
                  .HasForeignKey(e => e.ComponentId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(e => new { e.CollectionId, e.DisplayPreference }).IsUnique();
        });

        // FieldProperty Configuration
        modelBuilder.Entity<FieldProperty>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.PropertyName).IsRequired().HasMaxLength(100);

            entity.HasOne(e => e.Field)
                  .WithMany(e => e.FieldProperties)
                  .HasForeignKey(e => e.FieldId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(e => new { e.FieldId, e.PropertyName }).IsUnique();
        });

        // FieldValidation Configuration
        modelBuilder.Entity<FieldValidation>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ValidationType).IsRequired().HasMaxLength(100);

            entity.HasOne(e => e.Field)
                  .WithMany(e => e.FieldValidations)
                  .HasForeignKey(e => e.FieldId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }
}
```
```
```
