# Dynamic Form Configuration System - Database Tables

## Table Definitions

### 1. Collections Table
```sql
CREATE TABLE collections (
    id SERIAL PRIMARY KEY,
    collection_name VARCHAR(255) NOT NULL,
    collection_desc VARCHAR(500),
    collection_api_id VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. Field Types Table
```sql
CREATE TABLE field_types (
    id SERIAL PRIMARY KEY,
    field_type_name VARCHAR(100) NOT NULL UNIQUE,
    field_type_desc VARCHAR(500),
    display_name VARCHAR(100) NOT NULL,
    help_text VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. Components Table
```sql
CREATE TABLE components (
    id SERIAL PRIMARY KEY,
    component_name VARCHAR(255) NOT NULL,
    component_display_name VA<PERSON>HAR(255) NOT NULL,
    component_api_id VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    get_url VARCHAR(500),
    post_url VARCHAR(500),
    update_url VARCHAR(500),
    additional_information TEXT,
    additional_info_image VARCHAR(500),
    parent_component_id INT REFERENCES components(id),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4. Collection Components Table
```sql
CREATE TABLE collection_components (
    id SERIAL PRIMARY KEY,
    collection_id INT REFERENCES collections(id),
    component_id INT REFERENCES components(id),
    display_preference INT NOT NULL DEFAULT 0,
    is_repeatable BOOLEAN DEFAULT FALSE,
    min_repeat_occurrences INT,
    max_repeat_occurrences INT,
    is_active BOOLEAN DEFAULT TRUE,
    name VARCHAR(255),
    display_name VARCHAR(255),
    additional_info TEXT,
    additional_info_image VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 5. Fields Table
```sql
CREATE TABLE fields (
    id SERIAL PRIMARY KEY,
    field_type_id INT REFERENCES field_types(id),
    component_id INT REFERENCES components(id),
    collection_id INT REFERENCES collections(id),
    display_preference INT NOT NULL DEFAULT 0,
    field_name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 6. Field Properties Table
```sql
CREATE TABLE field_properties (
    id SERIAL PRIMARY KEY,
    field_id INT REFERENCES fields(id),
    property_name VARCHAR(100) NOT NULL,
    property_value TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 7. Field Validations Table
```sql
CREATE TABLE field_validations (
    id SERIAL PRIMARY KEY,
    field_id INT REFERENCES fields(id),
    validation_type VARCHAR(100) NOT NULL,
    validation_value TEXT,
    error_message VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Table Summary

| Table Name | Purpose |
|------------|---------|
| collections | Main form containers |
| field_types | Available field types (text, dropdown, etc.) |
| components | Reusable form components |
| collection_components | Links components to collections |
| fields | Individual form fields |
| field_properties | Field configuration properties |
| field_validations | Field validation rules |

## Field Data Types Used

| Data Type | Usage |
|-----------|-------|
| SERIAL | Auto-incrementing primary keys |
| INT | Foreign keys and numeric values |
| VARCHAR(n) | Text fields with length limits |
| TEXT | Large text content |
| BOOLEAN | True/false values |
| TIMESTAMP | Date and time values |
