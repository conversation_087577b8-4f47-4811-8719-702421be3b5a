# User Details Database Structure with Relationships

## Based on JSON Form Configuration

### 1. Collections Table
```sql
CREATE TABLE collections (
    id SERIAL PRIMARY KEY,
    collection_name VARCHAR(255) NOT NULL,
    collection_desc VARCHAR(500),
    collection_api_id VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. Field Types Table
```sql
CREATE TABLE field_types (
    id SERIAL PRIMARY KEY,
    field_type_name VARCHAR(100) NOT NULL,
    field_type_desc VARCHAR(500),
    display_name VARCHAR(100) NOT NULL,
    help_text VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE
);
```

### 3. Components Table
```sql
CREATE TABLE components (
    id SERIAL PRIMARY KEY,
    component_name VARCHAR(255) NOT NULL,
    component_display_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    component_api_id VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    get_url VARCHAR(500),
    post_url VARCHAR(500),
    update_url VARCHAR(500),
    additional_information TEXT,
    additional_info_image VARCHAR(500),
    parent_component_id INT REFERENCES components(id) ON DELETE SET NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4. Collection Components Table
```sql
CREATE TABLE collection_components (
    id SERIAL PRIMARY KEY,
    collection_id INT REFERENCES collections(id) ON DELETE CASCADE,
    component_id INT REFERENCES components(id) ON DELETE CASCADE,
    display_preference INT NOT NULL,
    is_repeatable BOOLEAN DEFAULT FALSE,
    min_repeat_occurrences INT,
    max_repeat_occurrences INT,
    is_active BOOLEAN DEFAULT TRUE,
    name VARCHAR(255),
    display_name VARCHAR(255),
    additional_info TEXT,
    additional_info_image VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 5. Fields Table
```sql
CREATE TABLE fields (
    id SERIAL PRIMARY KEY,
    field_type_id INT REFERENCES field_types(id) ON DELETE RESTRICT,
    component_id INT REFERENCES components(id) ON DELETE CASCADE,
    collection_id INT REFERENCES collections(id) ON DELETE CASCADE,
    display_preference INT NOT NULL,
    field_name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CHECK ((component_id IS NOT NULL AND collection_id IS NULL) OR
           (component_id IS NULL AND collection_id IS NOT NULL))
);
```

### 6. Field Properties Table
```sql
CREATE TABLE field_properties (
    id SERIAL PRIMARY KEY,
    field_id INT REFERENCES fields(id) ON DELETE CASCADE,
    property_name VARCHAR(100) NOT NULL,
    property_value TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(field_id, property_name)
);
```

### 7. Field Validations Table
```sql
CREATE TABLE field_validations (
    id SERIAL PRIMARY KEY,
    field_id INT REFERENCES fields(id) ON DELETE CASCADE,
    validation_type VARCHAR(100) NOT NULL,
    validation_value TEXT,
    is_required BOOLEAN DEFAULT FALSE,
    error_message VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 8. Designations Master Table
```sql
CREATE TABLE designations (
    id SERIAL PRIMARY KEY,
    designation_name VARCHAR(100) NOT NULL,
    designation_code VARCHAR(50) UNIQUE,
    designation_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 9. Entities Master Table
```sql
CREATE TABLE entities (
    id SERIAL PRIMARY KEY,
    entity_name VARCHAR(255) NOT NULL,
    entity_code VARCHAR(100) UNIQUE,
    entity_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 10. Roles Master Table
```sql
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    role_name VARCHAR(255) NOT NULL,
    role_code VARCHAR(100) UNIQUE,
    role_description TEXT,
    permissions TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 11. User Details Data Table (Actual User Data)
```sql
CREATE TABLE user_details (
    id SERIAL PRIMARY KEY,
    employee_code VARCHAR(100) UNIQUE,
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    email_id VARCHAR(255) UNIQUE NOT NULL,
    designation_id INT REFERENCES designations(id) ON DELETE SET NULL,
    is_super_user BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    modified_by INT REFERENCES user_details(id) ON DELETE SET NULL
);
```

### 12. User Entity Mapping Table (Many-to-Many)
```sql
CREATE TABLE user_entity_mapping (
    id SERIAL PRIMARY KEY,
    user_id INT REFERENCES user_details(id) ON DELETE CASCADE,
    entity_id INT REFERENCES entities(id) ON DELETE CASCADE,
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(user_id, entity_id)
);
```

### 13. User Role Mapping Table (Many-to-Many)
```sql
CREATE TABLE user_role_mapping (
    id SERIAL PRIMARY KEY,
    user_id INT REFERENCES user_details(id) ON DELETE CASCADE,
    role_id INT REFERENCES roles(id) ON DELETE CASCADE,
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(user_id, role_id)
);
```

### 14. User Sessions Table (Optional - for tracking user activity)
```sql
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INT REFERENCES user_details(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    logout_time TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    is_active BOOLEAN DEFAULT TRUE
);
```

### 15. Audit Log Table (Optional - for tracking changes)
```sql
CREATE TABLE audit_logs (
    id SERIAL PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    record_id INT NOT NULL,
    action VARCHAR(50) NOT NULL, -- INSERT, UPDATE, DELETE
    old_values JSONB,
    new_values JSONB,
    changed_by INT REFERENCES user_details(id) ON DELETE SET NULL,
    changed_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45)
);
```

## Complete Table Relationships

### Form Configuration Relationships:
```
collections (1) ←→ (M) collection_components (M) ←→ (1) components
                                                           ↓ (1)
                                                           ↓
field_types (1) ←→ (M) fields ←→ (1) components
                      ↓ (1)
                      ↓
                field_properties (M)
                field_validations (M)
```

### User Data Relationships:
```
designations (1) ←→ (M) user_details (1) ←→ (M) user_entity_mapping (M) ←→ (1) entities
                                    ↓ (1)
                                    ↓
                            user_role_mapping (M) ←→ (1) roles
                                    ↓ (1)
                                    ↓
                            user_sessions (M)
                            audit_logs (M)
```

### Detailed Relationship Constraints:

#### 1. Form Configuration Relationships:
- **collections** → **collection_components** (1:M, CASCADE DELETE)
- **components** → **collection_components** (1:M, CASCADE DELETE)
- **components** → **components** (1:M, SET NULL - parent/child)
- **field_types** → **fields** (1:M, RESTRICT DELETE)
- **components** → **fields** (1:M, CASCADE DELETE)
- **collections** → **fields** (1:M, CASCADE DELETE)
- **fields** → **field_properties** (1:M, CASCADE DELETE)
- **fields** → **field_validations** (1:M, CASCADE DELETE)

#### 2. User Data Relationships:
- **designations** → **user_details** (1:M, SET NULL)
- **user_details** → **user_entity_mapping** (1:M, CASCADE DELETE)
- **entities** → **user_entity_mapping** (1:M, CASCADE DELETE)
- **user_details** → **user_role_mapping** (1:M, CASCADE DELETE)
- **roles** → **user_role_mapping** (1:M, CASCADE DELETE)
- **user_details** → **user_sessions** (1:M, CASCADE DELETE)
- **user_details** → **audit_logs** (1:M, SET NULL)

#### 3. Self-Referencing Relationships:
- **components.parent_component_id** → **components.id** (SET NULL)
- **user_details.created_by** → **user_details.id** (SET NULL)
- **user_details.modified_by** → **user_details.id** (SET NULL)

## Field Mapping from JSON to Database:

| JSON Field | Database Table | Column | Field Type | Relationship |
|------------|----------------|--------|------------|--------------|
| Employee_Code | user_details | employee_code | VARCHAR(100) | Direct field |
| First_Name | user_details | first_name | VARCHAR(255) | Direct field |
| Last_Name | user_details | last_name | VARCHAR(255) | Direct field |
| Email_Id | user_details | email_id | VARCHAR(255) | Direct field with regex validation |
| Designation | user_details | designation_id | INT | Foreign key to designations table |
| Entity | user_entity_mapping | entity_id | INT | Many-to-many via mapping table |
| Role | user_role_mapping | role_id | INT | Many-to-many via mapping table |
| is_Super_User? | user_details | is_super_user | BOOLEAN | Direct boolean field |

## Indexes for Performance:

```sql
-- Form Configuration Indexes
CREATE INDEX idx_collections_api_id ON collections(collection_api_id);
CREATE INDEX idx_components_api_id ON components(component_api_id);
CREATE INDEX idx_fields_component_id ON fields(component_id);
CREATE INDEX idx_fields_collection_id ON fields(collection_id);
CREATE INDEX idx_field_properties_field_id ON field_properties(field_id);

-- User Data Indexes
CREATE INDEX idx_user_details_email ON user_details(email_id);
CREATE INDEX idx_user_details_employee_code ON user_details(employee_code);
CREATE INDEX idx_user_details_designation ON user_details(designation_id);
CREATE INDEX idx_user_entity_mapping_user ON user_entity_mapping(user_id);
CREATE INDEX idx_user_role_mapping_user ON user_role_mapping(user_id);
CREATE INDEX idx_user_sessions_user ON user_sessions(user_id);
CREATE INDEX idx_audit_logs_table_record ON audit_logs(table_name, record_id);
```

## Business Rules:

1. **User Email**: Must be unique across all users
2. **Employee Code**: Must be unique if provided
3. **Multi-Select Fields**: Entity and Role use mapping tables for many-to-many relationships
4. **Soft Delete**: Use is_active flag instead of hard deletes for user data
5. **Audit Trail**: All changes tracked in audit_logs table
6. **Session Management**: Track user login/logout activities
7. **Referential Integrity**: Proper CASCADE and SET NULL constraints maintain data consistency
